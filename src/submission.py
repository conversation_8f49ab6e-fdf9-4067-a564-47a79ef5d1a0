#!/usr/bin/python

import random
from typing import Callable, Dict, List, <PERSON><PERSON>, <PERSON>V<PERSON>, DefaultDict
from util import *

FeatureVector = Dict[str, int]
WeightVector = Dict[str, float]
Example = Tuple[FeatureVector, int]

############################################################
# Problem 1: binary classification
############################################################

############################################################
# Problem 1a: feature extraction


def extractWordFeatures(x: str) -> FeatureVector:
    """
    Extract word features for a string x. Words are delimited by
    whitespace characters only.
    @param string x:
    @return dict: feature vector representation of x.
    Example: "I am what I am" --> {'I': 2, 'am': 2, 'what': 1}
    """
    # ### START CODE HERE ###
    words = x.split()
    feature_vector = {}
    for word in words:
        if word in feature_vector:
            feature_vector[word] += 1
        else:
            feature_vector[word] = 1

    return feature_vector
    # ### END CODE HERE ###


############################################################
# Problem 1b: stochastic gradient descent

T = TypeVar("T")


def learnPredictor(
    trainExamples: List[Tuple[T, int]],
    validationExamples: List[Tuple[T, int]],
    featureExtractor: Callable[[T], FeatureVector],
    numEpochs: int,
    eta: float,
) -> WeightVector:
    """
    Given |trainExamples| and |validationExamples| (each one is a list of (x,y)
    pairs), a |featureExtractor| to apply to x, and the number of epochs to
    train |numEpochs|, the step size |eta|, return the weight vector (sparse
    feature vector) learned.

    You should implement stochastic gradient descent.

    Notes:
    - Only use the trainExamples for training!
    - You should call evaluatePredictor() on both trainExamples and validationExamples
    to see how you're doing as you learn after each epoch.
    - The predictor should output +1 if the score is precisely 0.
    """
    weights = {}  # feature => weight
    # ### START CODE HERE ###
    for epoch in range(numEpochs):
        for x, y in trainExamples:
            phi = featureExtractor(x)
            score = dotProduct(phi, weights)
            prediction = 1 if score >= 0 else -1

            # For perceptron loss only update weights when prediction is wrong OR margin is too small
            # Use a small margin to ensure learning even when technically correct
            margin = y * score
            if prediction != y or margin <= 0.01:
                # Gradient update: weights = weights + eta * y * phi
                increment(weights, eta * y, phi)
    # ### END CODE HERE ###
    return weights


############################################################
# Problem 1c: generate test case


def generateDataset(numExamples: int, weights: WeightVector) -> List[Example]:
    """
    Return a set of examples (phi(x), y) randomly which are classified correctly by
    |weights|.
    """
    random.seed(42)

    # Return a single example (phi(x), y).
    # phi(x) should be a dict whose keys are a subset of the keys in weights
    # and values can be anything (randomize!) with a score for the given weight vector.
    # y should be 1 or -1 as classified by the weight vector.
    # y should be 1 if the score is precisely 0.

    # Note that the weight vector can be arbitrary during testing.
    def generateExample() -> Tuple[Dict[str, int], int]:
        phi = None
        y = None
        # ### START CODE HERE ###
        phi = {}
        # select a random number of features from weights
        num_features = random.randint(1, len(weights))
        selected_features = random.sample(list(weights.keys()), num_features)

        for feature in selected_features:
            # assign random values to each of the selected features
            phi[feature] = random.randint(1, 5)
        
        # calculate the score and subsequent prediction
        score = dotProduct(phi, weights)
        y = 1 if score >= 0 else -1

        # ### END CODE HERE ###
        return (phi, y)

    return [generateExample() for _ in range(numExamples)]


############################################################
# Problem 1d: character features


def extractCharacterFeatures(n: int) -> Callable[[str], FeatureVector]:
    """
    Return a function that takes a string |x| and returns a sparse feature
    vector consisting of all n-grams of |x| without spaces mapped to their n-gram counts.
    EXAMPLE: (n = 3) "I like tacos" --> {'Ili': 1, 'lik': 1, 'ike': 1, ...
    You may assume that n >= 1.
    """

    def extract(x):
        # ### START CODE HERE ###
        ngrams = {}
        text = x.replace(" ", "")
        for letter in range(len(text) - n + 1):
            ngram = text[letter:letter+n]
            ngrams[ngram] = ngrams.get(ngram, 0) + 1

        return ngrams
        # ### END CODE HERE ###

    return extract


############################################################
# Problem 1e:
#
# Helper function to test 1e.
#
# To run this function, run the command from termial with `n` replaced
#
# $ python -c "from submission import *; testValuesOfN(n)"
#


def testValuesOfN(n: int):
    """
    Use this code to test different values of n for extractCharacterFeatures
    This code is exclusively for testing.
    Your full written solution for this problem must be submitted.
    """
    trainExamples = readExamples("polarity.train")
    validationExamples = readExamples("polarity.dev")
    featureExtractor = extractCharacterFeatures(n)
    weights = learnPredictor(
        trainExamples, validationExamples, featureExtractor, numEpochs=20, eta=0.01
    )
    outputWeights(weights, "weights")
    outputErrorAnalysis(
        validationExamples, featureExtractor, weights, "error-analysis"
    )  # Use this to debug
    trainError = evaluatePredictor(
        trainExamples,
        lambda x: (1 if dotProduct(featureExtractor(x), weights) >= 0 else -1),
    )
    validationError = evaluatePredictor(
        validationExamples,
        lambda x: (1 if dotProduct(featureExtractor(x), weights) >= 0 else -1),
    )
    print(
        (
            "Official: train error = %s, validation error = %s"
            % (trainError, validationError)
        )
    )


############################################################
# Problem 2b: K-means
############################################################


def kmeans(
    examples: List[Dict[str, float]], K: int, maxEpochs: int
) -> Tuple[List, List, float]:
    """
    examples: list of examples, each example is a string-to-float dict representing a sparse vector.
    K: number of desired clusters. Assume that 0 < K <= |examples|.
    maxEpochs: maximum number of epochs to run (you should terminate early if the algorithm converges).
    Return: (length K list of cluster centroids,
            list of assignments (i.e. if examples[i] belongs to centers[j], then assignments[i] = j),
            final reconstruction loss)
    """
    # ### START CODE HERE ###

    # Pre-compute example norms since they don't change
    example_norms = [dotProduct(ex, ex) for ex in examples]

    # Initialize centers randomly
    centers = [dict(examples[random.randint(0, len(examples) - 1)]) for _ in range(K)]
    assignments = [0] * len(examples)

    for epoch in range(maxEpochs):
        # Pre-compute center norms for this epoch
        center_norms = [dotProduct(center, center) for center in centers]

        new_assignments = []
        for ex_idx, example in enumerate(examples):
            best_center = 0
            best_dist_squared = float('inf')

            for center_idx, center in enumerate(centers):
                # Optimized distance calculation using dot product decomposition
                cross_term = dotProduct(example, center)
                dist_squared = example_norms[ex_idx] - 2 * cross_term + center_norms[center_idx]

                if dist_squared < best_dist_squared:
                    best_dist_squared = dist_squared
                    best_center = center_idx

            new_assignments.append(best_center)

        # Check for convergence
        if new_assignments == assignments:
            break

        assignments = new_assignments

        # Update centers
        new_centers = []
        for cluster_id in range(K):
            cluster_examples = [examples[i] for i in range(len(examples))
                              if assignments[i] == cluster_id]

            if cluster_examples:
                new_centers.append(calculate_centroid(cluster_examples))
            else:
                new_centers.append(centers[cluster_id])

        centers = new_centers

    # Calculate final loss
    total_loss = 0.0
    center_norms = [dotProduct(center, center) for center in centers]
    for i, example in enumerate(examples):
        center_idx = assignments[i]
        cross_term = dotProduct(example, centers[center_idx])
        total_loss += example_norms[i] - 2 * cross_term + center_norms[center_idx]

    return (centers, assignments, total_loss)

def calculate_centroid(points):
    if not points:
        return {}

    centroid = {}
    num_points = len(points)

    # Single pass through all points
    for point in points:
        for feature, value in point.items():
            centroid[feature] = centroid.get(feature, 0) + value

    # Divide by number of points to get average
    for feature in centroid:
        centroid[feature] /= num_points

    return centroid

    # ### END CODE HERE ###
