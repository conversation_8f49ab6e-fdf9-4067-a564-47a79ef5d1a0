\item {\bf Sentiment Classification}

In this problem, we will build a binary linear classifier that reads movie
reviews and guesses whether they are ``positive'' or ``negative.''

\textbf{Do not import any outside libraries (e.g. numpy) for any of the coding parts.}
Only standard python libraries and/or the libraries imported in the starter code are allowed. In this problem, you must implement the functions without using libraries like Scikit-learn.

\begin{enumerate}

  \input{01-sentiment-classification/01-extractWordFeatures}

  \input{01-sentiment-classification/02-learnPredictor}

  \input{01-sentiment-classification/03-generateExample}

  \input{01-sentiment-classification/04-extractCharacterFeatures}

  \input{01-sentiment-classification/05-outperform}

\end{enumerate}
