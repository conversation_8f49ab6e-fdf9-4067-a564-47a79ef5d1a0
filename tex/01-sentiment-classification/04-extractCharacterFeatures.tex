\item \points{1d}

Now we will try a crazier feature extractor.  Some languages are written without
spaces between words. So is splitting the words really necessary or can we just
naively consider strings of characters that stretch across words? Implement the
function |extractCharacterFeatures| (by filling in the |extract|
function), which maps each string of $n$ characters to the number of times it
occurs, ignoring whitespace (spaces and tabs).
