\item {\bf K-means clustering}

Suppose we have a feature extractor $\phi$ that produces 2-dimensional feature
vectors, and a toy dataset $\mathcal D_\text{train} = \{x_1, x_2, x_3, x_4\}$
with
\begin{itemize}
  \item $\phi(x_1) = [10, 0]$
  \item $\phi(x_2) = [30, 0]$
  \item $\phi(x_3) = [10, 20]$
  \item $\phi(x_4) = [20, 20]$
\end{itemize}

\begin{enumerate}

  \input{02-k-means-clustering/01-kmeans-with-2-means}

  \input{02-k-means-clustering/02-implement-kmeans}

\end{enumerate}
