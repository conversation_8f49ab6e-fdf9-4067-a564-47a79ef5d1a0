\item \points{2b}

Implement the |kmeans| function. You should initialize your $k$
cluster centers to random elements of |examples|.


After a few iterations of k-means, your centers will be very dense
vectors. In order for your code to run efficiently and to obtain full
credit, you will need to precompute certain dot products. As a reference,
our code runs in under a second on cardinal, on all test cases. You might
find |generateClusteringExamples|
in |util.py| useful for testing your code.

\textbf{Do not} use libraries such as Scikit-learn.