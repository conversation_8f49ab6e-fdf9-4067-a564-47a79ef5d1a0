🐍
import json

# Load the meta.json for this assignment
with open(f'../meta.json', 'r') as f:
  meta = json.load(f)

if "preamble" in meta:
  print(meta['tex_preamble'])

if meta['tex_show_guidelines']:
  print(
R'''\input{00-instructions/01-general}
\bigskip
''')

if meta['tex_show_submission_instructions']:
  print(R'''
\input{00-instructions/02-submission}
\bigskip
''')

if meta['tex_show_honor_code']:
  print(
R'''\input{00-instructions/03-honor-code}
\bigskip
''')

if meta['tex_show_autograder_instructions']:
  print(R'''
\input{00-instructions/04-autograder}
\bigskip''')


if meta['tex_show_test_cases']:
  print(R'''
\input{00-instructions/05-test-cases}
\bigskip''')
🐍