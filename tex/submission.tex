% This contents of this file will be inserted into the _Solutions version of the
% output tex document.  Here's an example:

% If assignment with subquestion (1.a) requires a written response, you will
% find the following flag within this document: <SCPD_SUBMISSION_TAG>_1a
% In this example, you would insert the LaTeX for your solution to (1.a) between
% the <SCPD_SUBMISSION_TAG>_1a flags.  If you also constrain your answer between the
% START_CODE_HERE and END_CODE_HERE flags, your LaTeX will be styled as a
% solution within the final document.

% Please do not use the '<SCPD_SUBMISSION_TAG>' character anywhere within your code.  As expected,
% that will confuse the regular expressions we use to identify your solution.

\def\assignmentnum{1 }
\def\assignmentname{Sentiment Analysis}
\def\assignmenttitle{XCS221 Assignment \assignmentnum --- \assignmentname}
\input{macros}
\begin{document}
\pagestyle{myheadings} \markboth{}{\assignmenttitle}

% <SCPD_SUBMISSION_TAG>_entire_submission

This handout includes space for every question that requires a written response.
Please feel free to use it to handwrite your solutions (legibly, please).  If
you choose to typeset your solutions, the |README.md| for this assignment includes
instructions to regenerate this handout with your typeset \LaTeX{} solutions.
\ruleskip

\LARGE
1.e
\normalsize

% <SCPD_SUBMISSION_TAG>_1e
\begin{answer}
  % ### START CODE HERE ###
  % ### END CODE HERE ###
\end{answer}
% <SCPD_SUBMISSION_TAG>_1e
\clearpage

\LARGE
2.a
\normalsize

% <SCPD_SUBMISSION_TAG>_2a
\begin{answer}
  % ### START CODE HERE ###
  % ### END CODE HERE ###
\end{answer}
% <SCPD_SUBMISSION_TAG>_2a
\clearpage

% <SCPD_SUBMISSION_TAG>_entire_submission
\end{document}