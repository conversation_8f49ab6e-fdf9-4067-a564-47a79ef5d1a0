\def\assignmentnum{1 }
\def\assignmentname{Sentiment Analysis}
\def\assignmenttitle{XCS221 Assignment \assignmentnum --- \assignmentname}


\input{macros}
\input{py-macros}

\begin{document}

\pagestyle{myheadings} \markboth{}{\assignmenttitle}

{\huge\noindent \assignmenttitle}

\ruleskip

{\bf Due {\due }.}

\medskip

\input{00-instructions/00-main}

\ruleskip

\clearpage

\include{00-intro}
\clearpage

\begin{enumerate}[wide, labelindent=0pt]

  \input{01-sentiment-classification/00-main}
  \clearpage

  \input{02-k-means-clustering/00-main}
  \clearpage

\end{enumerate}

\include{00-outro}

\end{document}
